import React from 'react';
import AntdTable from './AntdTable';

/**
 * 粘性表格演示组件
 * 用于测试表格的固定头部和底部功能
 */
const StickyTableDemo: React.FC = () => {
  return (
    <div style={{ height: '100vh', padding: '20px' }}>
      <h1 style={{ marginBottom: '20px', textAlign: 'center' }}>
        粘性表格演示 - 固定搜索区域和操作区域
      </h1>
      <div style={{ 
        height: 'calc(100vh - 100px)', 
        border: '1px solid #d9d9d9', 
        borderRadius: '8px',
        overflow: 'hidden'
      }}>
        <AntdTable />
      </div>
      <div style={{ 
        marginTop: '10px', 
        textAlign: 'center', 
        color: '#666',
        fontSize: '14px'
      }}>
        当表格数据较多时，搜索区域和操作区域将保持固定在顶部，分页区域固定在底部
      </div>
    </div>
  );
};

export default StickyTableDemo;
